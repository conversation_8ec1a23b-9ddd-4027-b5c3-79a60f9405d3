import { Controller, Get, Query, Param, UseGuards } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation, ApiResponse, ApiQuery, ApiParam } from '@nestjs/swagger';
import { TeamService } from '../services/team.service';
import { GetTeamsDto, PaginatedTeamsResponse, TeamResponseDto } from '../models/team.dto';
import { TeamStatisticsService } from '../services/team-statistics.service';
import { GetTeamStatisticsDto, TeamStatisticsResponseDto } from '../models/team-statistics.dto';
import { SystemJwtAuthGuard } from '../../../auth/system/guards/system-jwt-auth.guard';
import { SystemRolesGuard } from '../../../auth/system/guards/system-roles.guard';
import { TierAccessGuard } from '../../../auth/users/guards/tier-access.guard';
import { Public } from '../../../auth/core/decorators/auth.decorators';

@ApiTags('Football - Teams')
@Controller('football/teams')
@UseGuards(SystemJwtAuthGuard, SystemRolesGuard, TierAccessGuard)
export class TeamController {
    constructor(
        private readonly teamService: TeamService,
        private readonly teamStatisticsService: TeamStatisticsService
    ) { }


    @ApiOperation({
        summary: 'Get Teams with Filters (Public)',
        description: `
        Retrieve teams with comprehensive filtering options.

        **🔓 PUBLIC ACCESS:**
        This endpoint is publicly accessible and does not require authentication.

        **Features:**
        - Complete team database
        - League-based filtering
        - Country-based filtering
        - Search by team name, country, or code
        - Pagination support
        - No authentication required

        **Query Parameters:**
        - page, limit: Pagination
        - league, season: Filter by league/season
        - country: Filter by country name
        - search: Search by team name, country, or code
        - newdb: Force fetch from API and update database (optional, default: false)

        **Search Examples:**
        - ?search=Manchester (Find Manchester United, Manchester City)
        - ?search=United (Find Manchester United, Newcastle United, etc.)
        - ?search=MUN (Find by team code)
        - ?search=England (Find English teams)
        - ?search=Flamengo&newdb=true (Force fetch Flamengo from API)

        **Filter Examples:**
        - ?league=39&season=2024 (Premier League 2024 teams)
        - ?country=England (All English teams)
        - ?league=140&season=2024 (La Liga 2024 teams)
        - ?search=Flamengo&newdb=true (Force refresh Flamengo data)

        **Use Cases:**
        - Team directory for mobile apps
        - Website team listings
        - Third-party integrations
        - Sports data aggregation
        - Public team information access

        **Example Request:**
        \`\`\`
        GET /football/teams?league=39&season=2024 HTTP/1.1
        \`\`\`
        `
    })
    @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
    @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page', example: 10 })
    @ApiQuery({ name: 'league', required: false, type: Number, description: 'League ID', example: 39 })
    @ApiQuery({ name: 'season', required: false, type: Number, description: 'Season year', example: 2024 })
    @ApiQuery({ name: 'country', required: false, type: String, description: 'Country name', example: 'England' })
    @ApiQuery({ name: 'search', required: false, type: String, description: 'Search teams by name, country, or code (case-insensitive)', example: 'Manchester' })
    @ApiQuery({
        name: 'newdb',
        required: false,
        type: Boolean,
        description: 'Force fetch from API and update database',
        example: true
    })
    @ApiResponse({
        status: 200,
        description: 'Teams retrieved successfully',
        example: {
            data: [
                {
                    id: 1,
                    externalId: 33,
                    name: 'Manchester United',
                    code: 'MUN',
                    country: 'England',
                    founded: 1878,
                    logo: 'https://media.api-sports.io/football/teams/33.png'
                }
            ],
            meta: { totalItems: 20, totalPages: 2, currentPage: 1, limit: 10 }
        }
    })
    @Public()
    @Get()
    async getTeams(@Query() query: GetTeamsDto): Promise<PaginatedTeamsResponse> {
        return this.teamService.getTeams(query);
    }

    @ApiOperation({
        summary: 'Get Team Statistics',
        description: `
        Retrieve detailed team statistics for a specific league and season.

        **Features:**
        - Complete team performance data
        - League-specific statistics
        - Season-based analysis
        - Authentication required for API usage tracking

        **Tier Access:**
        - Free: 100 API calls/month
        - Premium: 10,000 API calls/month
        - Enterprise: Unlimited API calls

        **Required Parameters:**
        - league: League ID (e.g., 39 for Premier League)
        - season: Season year (e.g., 2024)
        - team: Team ID (e.g., 33 for Manchester United)

        **Examples:**
        - ?league=39&season=2024&team=33 (Manchester United in Premier League 2024)
        - ?league=140&season=2024&team=529 (Barcelona in La Liga 2024)
        `
    })
    @ApiQuery({ name: 'league', required: true, type: Number, description: 'League ID', example: 39 })
    @ApiQuery({ name: 'season', required: true, type: Number, description: 'Season year', example: 2024 })
    @ApiQuery({ name: 'team', required: true, type: Number, description: 'Team ID', example: 33 })
    @ApiResponse({
        status: 200,
        description: 'Team statistics retrieved successfully',
        example: {
            data: {
                teamId: 33,
                leagueId: 39,
                season: 2024,
                fixtures: {
                    played: { home: 19, away: 19, total: 38 },
                    wins: { home: 12, away: 8, total: 20 },
                    draws: { home: 4, away: 6, total: 10 },
                    loses: { home: 3, away: 5, total: 8 }
                },
                goals: {
                    for: { total: { home: 35, away: 22, total: 57 } },
                    against: { total: { home: 18, away: 25, total: 43 } }
                }
            },
            status: 200
        }
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid query parameters',
        example: {
            message: 'league, season, and team parameters are required',
            error: 'Bad Request',
            statusCode: 400
        }
    })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - Authentication required',
        example: {
            message: 'System authentication required',
            error: 'Unauthorized',
            statusCode: 401
        }
    })
    @ApiResponse({
        status: 404,
        description: 'Team statistics not found',
        example: {
            message: 'Team statistics not found for the specified parameters',
            error: 'Not Found',
            statusCode: 404
        }
    })
    @ApiBearerAuth('bearer')
    @Get('statistics')
    async getTeamStatistics(
        @Query() query: GetTeamStatisticsDto,
    ): Promise<{ data: TeamStatisticsResponseDto; status: number }> {
        return this.teamStatisticsService.getTeamStatistics(query);
    }

    @ApiOperation({
        summary: 'Get Team by ID (Public)',
        description: `
        Retrieve detailed information for a specific team by external ID.

        **🔓 PUBLIC ACCESS:**
        This endpoint is publicly accessible and does not require authentication.

        **Features:**
        - Complete team profile
        - Team statistics and information
        - Logo and branding data
        - No authentication required

        **Parameter:**
        - externalId: Team external ID (positive integer)

        **Query Parameters:**
        - newdb: Force fetch from API and update database (optional, default: false)

        **Examples:**
        - /33 (Manchester United)
        - /529 (Barcelona)
        - /50 (Manchester City)
        - /2385?newdb=true (Force fetch from API)

        **Use Cases:**
        - Team profile pages
        - Mobile app team details
        - Website team information
        - Third-party integrations
        - Force refresh team data with newdb=true
        `
    })
    @ApiParam({ name: 'externalId', type: 'number', description: 'Team external ID', example: 33 })
    @ApiQuery({
        name: 'newdb',
        required: false,
        type: Boolean,
        description: 'Force fetch from API and update database',
        example: true
    })
    @ApiResponse({
        status: 200,
        description: 'Team retrieved successfully',
        example: {
            data: {
                id: 1,
                externalId: 33,
                name: 'Manchester United',
                code: 'MUN',
                country: 'England',
                founded: 1878,
                national: false,
                logo: 'https://media.api-sports.io/football/teams/33.png'
            },
            status: 200
        }
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid team ID',
        example: {
            message: 'Invalid externalId: must be a positive integer',
            error: 'Bad Request',
            statusCode: 400
        }
    })
    @ApiResponse({
        status: 404,
        description: 'Team not found',
        example: {
            message: 'Team not found',
            error: 'Not Found',
            statusCode: 404
        }
    })
    @Public()
    @Get(':externalId')
    async getTeamById(
        @Param('externalId') externalId: number,
        @Query('newdb') newdb?: boolean
    ): Promise<{ data: TeamResponseDto; status: number }> {
        const team = await this.teamService.getTeamById(externalId, newdb);
        return { data: team, status: 200 };
    }
}