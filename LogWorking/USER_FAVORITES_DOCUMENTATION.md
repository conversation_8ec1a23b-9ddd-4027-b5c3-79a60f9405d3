# 📋 User Favorites System - Complete Documentation

## 🎯 Tổng Quan (Overview)

Hệ thống User Favorites cho phép người dùng đã đăng ký lưu lại các:
- **Teams** (<PERSON><PERSON><PERSON> bóng) 
- **Leagues** (<PERSON><PERSON><PERSON><PERSON> đấu)
- **Players** (<PERSON><PERSON><PERSON> thủ)

Hệ thống hỗ trợ **notification settings** để thông báo về fixtures, news, và transfers.

---

## 🔐 Authentication Required

**⚠️ TẤT CẢ endpoints đều yêu cầu authentication:**
```http
Authorization: Bearer <user_jwt_token>
```

---

## 📊 Database Schema

### UserFavorite Entity
```typescript
{
  id: number                        // Primary key
  userId: number                    // User ID (FK)
  entityType: 'team'|'league'|'player'  // Loại entity
  entityId: number                  // External ID từ API-Sports.io
  entityName: string                // Tên cached (VD: "Manchester United")
  entityLogo: string                // Logo URL cached
  isActive: boolean                 // Bật/tắt favorite (default: true)
  notifyFixtures: boolean           // Thông báo fixtures (default: true)
  notifyNews: boolean               // Thông báo news (default: false)
  notifyTransfers: boolean          // Thông báo transfers (default: false)
  createdAt: Date
  updatedAt: Date
}
```

---

## 🚀 API Endpoints

### 1. ➕ Thêm Favorite
```http
POST /user/favorites
Content-Type: application/json
Authorization: Bearer <token>

{
  "entityType": "team",              // Required: "team" | "league" | "player"
  "entityId": 33,                    // Required: External ID từ API-Sports.io
  "entityName": "Manchester United", // Optional: Tên để cache
  "entityLogo": "https://media.api-sports.io/football/teams/33.png", // Optional
  "notifyFixtures": true,            // Optional: default true
  "notifyNews": false,               // Optional: default false
  "notifyTransfers": false           // Optional: default false
}
```

**Response:**
```json
{
  "data": {
    "id": 1,
    "entityType": "team",
    "entityId": 33,
    "entityName": "Manchester United",
    "entityLogo": "https://media.api-sports.io/football/teams/33.png",
    "isActive": true,
    "notifyFixtures": true,
    "notifyNews": false,
    "notifyTransfers": false,
    "createdAt": "2025-06-27T10:00:00Z",
    "updatedAt": "2025-06-27T10:00:00Z"
  },
  "status": 201,
  "message": "Favorite added successfully"
}
```

### 2. 📋 Lấy Danh Sách Favorites
```http
GET /user/favorites?entityType=team&isActive=true&limit=20&offset=0
Authorization: Bearer <token>
```

**Query Parameters:**
- `entityType` (optional): `"team"` | `"league"` | `"player"`
- `isActive` (optional): `true` | `false`
- `limit` (optional): số lượng records (default: 50)
- `offset` (optional): bỏ qua records (default: 0)

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "entityType": "team",
      "entityId": 33,
      "entityName": "Manchester United",
      "entityLogo": "https://media.api-sports.io/football/teams/33.png",
      "isActive": true,
      "notifyFixtures": true,
      "notifyNews": false,
      "notifyTransfers": false,
      "createdAt": "2025-06-27T10:00:00Z",
      "updatedAt": "2025-06-27T10:00:00Z"
    }
  ],
  "status": 200
}
```

### 3. 📊 Thống Kê Favorites
```http
GET /user/favorites/stats
Authorization: Bearer <token>
```

**Response:**
```json
{
  "data": {
    "totalFavorites": 15,
    "totalTeams": 8,
    "totalLeagues": 5,
    "totalPlayers": 2,
    "activeFavorites": 12,
    "inactiveFavorites": 3,
    "favoritesWithFixtureNotifications": 10,
    "favoritesWithNewsNotifications": 3,
    "favoritesWithTransferNotifications": 1
  },
  "status": 200
}
```

### 4. ✏️ Cập Nhật Favorite
```http
PATCH /user/favorites/{favoriteId}
Content-Type: application/json
Authorization: Bearer <token>

{
  "isActive": false,           // Tắt favorite tạm thời
  "notifyFixtures": false,     // Tắt thông báo fixtures
  "notifyNews": true,          // Bật thông báo news
  "notifyTransfers": true      // Bật thông báo transfers
}
```

### 5. ❌ Xóa Favorite
```http
DELETE /user/favorites/{favoriteId}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "status": 200,
  "message": "Favorite removed successfully"
}
```

### 6. ✅ Kiểm Tra Favorite Status
```http
GET /user/favorites/check/{entityType}/{entityId}
Authorization: Bearer <token>
```

**Examples:**
- `GET /user/favorites/check/team/33` - Kiểm tra team Manchester United
- `GET /user/favorites/check/league/39` - Kiểm tra Premier League
- `GET /user/favorites/check/player/276` - Kiểm tra player Messi

**Response:**
```json
{
  "data": {
    "isFavorited": true,
    "entityType": "team",
    "entityId": 33
  },
  "status": 200
}
```

---

## 🔄 Integration với Fixtures

Khi gọi API fixture detail, response sẽ include `favoriteStatus` nếu user đã login:

```http
GET /football/fixtures/1145509
Authorization: Bearer <token>
```

**Response sẽ có thêm:**
```json
{
  "data": {
    // ... fixture data ...
    "favoriteStatus": {
      "homeTeamFavorited": true,    // User đã favorite home team
      "awayTeamFavorited": false,   // User chưa favorite away team  
      "leagueFavorited": true       // User đã favorite league này
    }
  }
}
```

---

## 📱 Use Cases Thực Tế

### 1. **Theo Dõi Đội Bóng Yêu Thích**
```javascript
// Thêm Manchester United vào favorites
const response = await fetch('/user/favorites', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + userToken,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    entityType: 'team',
    entityId: 33,  // Man United external ID
    entityName: 'Manchester United',
    notifyFixtures: true,  // Thông báo khi có trận đấu
    notifyTransfers: true  // Thông báo khi có chuyển nhượng
  })
});
```

### 2. **Theo Dõi Giải Đấu**
```javascript
// Thêm Premier League vào favorites
await fetch('/user/favorites', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + userToken,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    entityType: 'league',
    entityId: 39,  // Premier League external ID
    entityName: 'Premier League',
    notifyFixtures: false,  // Không thông báo fixtures (quá nhiều)
    notifyNews: true        // Chỉ thông báo news
  })
});
```

### 3. **Kiểm Tra Favorite trong Frontend**
```javascript
// Kiểm tra xem user đã favorite team này chưa
const checkFavorite = async (teamId) => {
  const response = await fetch(`/user/favorites/check/team/${teamId}`, {
    headers: { 'Authorization': 'Bearer ' + userToken }
  });
  const data = await response.json();
  return data.data.isFavorited;
};

// Sử dụng để show/hide favorite button
const isFavorited = await checkFavorite(33);
if (isFavorited) {
  showRemoveFavoriteButton();
} else {
  showAddFavoriteButton();
}
```

### 4. **Lấy Danh Sách Teams Yêu Thích**
```javascript
// Lấy tất cả teams user đã favorite
const getFavoriteTeams = async () => {
  const response = await fetch('/user/favorites?entityType=team&isActive=true', {
    headers: { 'Authorization': 'Bearer ' + userToken }
  });
  const data = await response.json();
  return data.data;
};

// Hiển thị trong My Teams section
const favoriteTeams = await getFavoriteTeams();
favoriteTeams.forEach(team => {
  addTeamToMyTeamsSection(team);
});
```

---

## ⚠️ Error Handling

### Common Errors:
```json
// 401 - Không có token hoặc token invalid
{
  "message": "System authentication required",
  "error": "Unauthorized", 
  "statusCode": 401
}

// 409 - Entity đã tồn tại trong favorites
{
  "message": "team with ID 33 is already in favorites",
  "error": "Conflict",
  "statusCode": 409
}

// 404 - Favorite không tồn tại
{
  "message": "Favorite with ID 999 not found",
  "error": "Not Found",
  "statusCode": 404
}
```

---

## 🎯 Best Practices

### 1. **Cache Entity Information**
Luôn truyền `entityName` và `entityLogo` khi add favorite để có thể hiển thị nhanh không cần query API.

### 2. **Notification Settings**
- `notifyFixtures`: Bật cho teams quan trọng
- `notifyNews`: Bật cho leagues/teams user quan tâm tin tức
- `notifyTransfers`: Chỉ bật cho teams đặc biệt quan trọng (tránh spam)

### 3. **Active Status**
Sử dụng `isActive: false` để tạm thời tắt favorite thay vì xóa hoàn toàn.

### 4. **Pagination**
Với user có nhiều favorites, sử dụng `limit` và `offset` để phân trang.

---

## 🔧 Technical Implementation

### External IDs Mapping:
- **Teams**: Sử dụng `team.externalId` từ API-Sports.io (VD: Manchester United = 33)
- **Leagues**: Sử dụng `league.externalId` từ API-Sports.io (VD: Premier League = 39) 
- **Players**: Sử dụng `player.externalId` từ API-Sports.io (VD: Messi = 276)

### Database Indexes:
- `(userId, entityType)` - Fast lookup user's favorites by type
- `(entityType, entityId)` - Fast lookup who favorited specific entity
- `(userId, entityType, entityId)` - Unique constraint

### Caching Strategy:
- Cache bị clear khi add/update/remove favorite
- Cache key pattern: `user_favorites_{userId}_*`

---

## 📝 Notes

1. **Unique Constraint**: Mỗi user chỉ có thể favorite 1 entity 1 lần
2. **Cascade Delete**: Khi xóa user, tất cả favorites sẽ bị xóa theo
3. **Real-time**: Hệ thống sẵn sàng cho notification system trong tương lai
4. **API Integration**: Favorites seamlessly integrated với fixture details API
