import { Module } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { FootballModule } from './football.module';
import { FixtureController } from './controllers/fixture.controller';
import { LeagueController } from './controllers/league.controller';
import { TeamController } from './controllers/team.controller';
import { TeamStatisticsController } from './controllers/team-statistics.controller';
import { PlayerController } from './controllers/player.controller';
import { StandingController } from './controllers/standing.controller';
import { AuthModule } from '../../auth/auth.module';
// Interceptors will be imported from shared module when available

// Football API Module - Contains controllers for HTTP endpoints
@Module({
  imports: [FootballModule, AuthModule],
  controllers: [
    FixtureController,
    LeagueController,
    TeamController,
    TeamStatisticsController,
    PlayerController,
    StandingController,
  ],
  providers: [
    // Interceptors will be added when available
  ],
})
export class FootballApiModule { }
